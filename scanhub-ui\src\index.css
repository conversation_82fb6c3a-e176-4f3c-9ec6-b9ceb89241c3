html,
body {
    height: 100%;
    margin: 0;
    padding: 0;
    overflow: auto;
    font-size: 16px;
    color: #333;
}

body {
    font-family: 'Segoe UI', sans-serif;
    display: flex;
}

/* Ensure smooth scrolling across the app */
* {
    scroll-behavior: smooth;
}

/* Fix for iOS Safari scrolling */
@supports (-webkit-touch-callout: none) {
    * {
        -webkit-overflow-scrolling: touch;
    }
}

.nav-tabs {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    width: 100%;
}

.nav-tabs .nav-item {
    width: 50%;
}

.nav-tabs .nav-link {
    color: #666;
    font-weight: 500;
    text-align: center;
    border: none;
    width: 100%;
}

.nav-tabs .nav-link.active {
    color: #000;
    position: relative;
    background: transparent;
    font-weight: 600;
}

.nav-tabs .nav-link.active::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 2px;
    background-color: #8e113d;
}

.cursor-pointer {
    cursor: pointer;
}

.btn-link {
    text-decoration: none;
}

.back-btn-link::before {
    content: '\2039';
    color: #8e113d;
    margin-right: 8px;
    font-size: larger;
}

.custom-color-blue {
    color: #005f85 !important;
}

.custom-color-maroon {
    color: #8e113d !important;
}

.custom-bg-blue {
    background-color: #005f85 !important;
}

.custom-bg-maroon {
    background-color: #8e113d !important;
}

.custom-bg-light-gray {
    background-color: #f9f9f9 !important;
}

/* Loader styles */
.loader-wrapper {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.75);
    /* Black with 50% transparency */
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    /* Ensure it's above other content */
}

.loader {
    border: 5px solid #f3f3f3;
    /* Light grey */
    border-bottom: 5px solid #6835c9;
    /* Orange */
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 2s linear infinite;
    /* Spin animation */
}
/* Loader ends */
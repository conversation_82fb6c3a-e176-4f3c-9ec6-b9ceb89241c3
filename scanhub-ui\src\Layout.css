html,
body {
    height: 100%;
    margin: 0;
    padding: 0;
    overflow: auto;
    font-size: 16px;
}

body {
    font-family: 'Segoe UI', sans-serif;
    display: flex;
}

.nav-tabs {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    width: 100%;
}

.nav-tabs .nav-item {
    width: 50%;
}

.nav-tabs .nav-link {
    color: #666;
    font-weight: 500;
    text-align: center;
    border: none;
    width: 100%;
}

.nav-tabs .nav-link.active {
    color: #000;
    position: relative;
    background: transparent;
    font-weight: 600;
}

.nav-tabs .nav-link.active::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 2px;
    background-color: #8e113d;
}

.custom-color-blue {
    color: #005f85 !important;
}

.custom-color-maroon {
    color: #8e113d !important;
}

.custom-bg-blue {
    background-color: #005f85 !important;
}

.custom-bg-maroon {
    background-color: #8e113d !important;
}

.custom-bg-light-gray {
    background-color: #f9f9f9 !important;
}

/* Safari-specific fixes */
@supports (-webkit-touch-callout: none) {
    .nav-tabs {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-justify-content: space-evenly;
        justify-content: space-evenly;
        width: 100%;
        position: relative;
        z-index: 1;
    }

    .nav-tabs .nav-item {
        -webkit-box-flex: 1;
        -webkit-flex: 1;
        flex: 1;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
    }

    .nav-tabs .nav-link {
        width: 100%;
        -webkit-appearance: none;
        appearance: none;
        border-radius: 0;
    }
}

/* Ensure proper display on iOS devices */
@media not all and (min-resolution:.001dpcm) { 
    @supports (-webkit-appearance:none) {
        .nav-tabs {
            -webkit-transform: translateZ(0);
            transform: translateZ(0);
        }
    }
}
.header {
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eee;
  /* background-color: #fff; */
}

.header .logo {
  display: flex;
  align-items: center;
}

.header .logo span {
  font-weight: bold;
}

.header .logo strong {
  padding: 0 6px;
  border-radius: 4px;
  font-weight: 600;
}

.header .logo img {
  width: 25px;
  height: 25px;
  margin-right: 10px;
}

/* iOS Safari specific header fixes */
@supports (-webkit-touch-callout: none) {
  .navbar.fixed-top {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 1030;
  }
}
.facility-list {
  flex-grow: 1;
  overflow-y: auto;
  padding: 1rem;
  max-height: calc(100vh - 200px);
  min-height: 400px;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
  scroll-behavior: smooth;
}

.facility {
  cursor: pointer;
}

.facility img {
  width: 60px;
  height: 40px;
  object-fit: cover;
  border-radius: 5px;
}

.pin {
  font-size: 1.2rem;
  padding-left: 1rem;
  display: inline-block;
  width: 40px;
  height: 24px;
  position: relative;
  text-align: center;
}

.pin svg {
  transition: all 0.2s ease-in-out;
}

.facility-details h4 {
  font-weight: 600;
}

/* Ensure the main container allows scrolling */
.desktop-container {
  overflow: visible;
}

/* Ensure tab content is scrollable */
.tab-content {
  overflow: visible;
}

.tab-pane {
  overflow: visible;
}


@media (max-width: 768px) {
  .facility-list {
      padding: 0.5rem;
      max-height: calc(100vh - 180px);
      min-height: 300px;
  }
}

@media (max-width: 576px) {
  .facility-list {
      padding: 0.5rem;
      max-height: calc(100vh - 160px);
      min-height: 250px;
  }
}
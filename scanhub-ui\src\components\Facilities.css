.facility-list {
  flex-grow: 1;
  overflow-y: auto;
  padding: 1rem;
  height: calc(100vh - 60px);
}

.facility {
  cursor: pointer;
}

.facility img {
  width: 60px;
  height: 40px;
  object-fit: cover;
  border-radius: 5px;
}

.pin {
  font-size: 1.2rem;
  padding-left: 1rem;
  display: inline-block;
  width: 40px;
  height: 24px;
  position: relative;
  text-align: center;
}

.pin svg {
  transition: all 0.2s ease-in-out;
}

.facility-details h4 {
  font-weight: 600;
}


@media (max-width: 768px) {
  .facility-list {
      padding: 0.5rem;
      height: calc(100vh - 50px);
  }
}

@media (max-width: 576px) {
  .facility-list {
      padding: 0.5rem;
      height: calc(100vh - 50px);
  }
}
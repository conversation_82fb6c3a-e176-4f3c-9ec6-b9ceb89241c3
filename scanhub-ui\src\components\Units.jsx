import { React, useEffect, useState, useContext } from "react";
import './Units.css';
import { useNavigate, useLocation } from 'react-router-dom';
import { getAllUnits, updateUnitAsFavorite } from '../services/common.services';
import globalContext from '../GlobalContext';
import Swal from 'sweetalert2';
import withReactContent from 'sweetalert2-react-content';
import { faThumbtack,faThumbtackSlash } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { logout } from '../services/authentication.service';

const Units = () => {
    const location = useLocation();
    const GlobalContext = useContext(globalContext);
    let navigate = useNavigate();
    const [listOfAllUnits, setListOfAllUnits] = useState([]);
    const MySwal = withReactContent(Swal);
    const [facilityObj,setFacilityObj]=useState({});
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedUnits, setSelectedUnits] = useState([]);
    const [isMyFaciliitesTabActive, setIsMyFaciliitesTabActive] = useState(true);
    const [isPinndedTabActive, setIsPinndedTabActive] = useState(true);

    useEffect(() => {
        setFacilityObj(location.state.facilityObj);
        setIsMyFaciliitesTabActive(location.state.isMyFaciliitesTabActive);
        setIsPinndedTabActive(location.state.isPinndedTabActive);
        setSelectedUnits(location.state.selectedUnits);
        fetchData();
    }, []);

    const fetchData = async () => {
        try {
            GlobalContext.startSpinner();
            let request = {
                Param1: String(location.state.facilityObj.Facility_ID)
            };
            await getAllUnits(request).then((resp) => {
                const list=resp.data ? resp.data : [];
                const sUnits = location.state.selectedUnits;
                if (sUnits.length > 0) {
                    sUnits.map(item => {
                        let targetItem = list.find(x => x.Unit_Id == item.Unit_Id);
                        targetItem.Selected = true;
                    });
                }
                setListOfAllUnits(list);
                GlobalContext.stopSpinner();
            }).catch((error) => {
                const status = error.response ? error.response.status : null;
                if (status === 403) { navigate('/forbidden'); }
                if (status === 401) { alert('session expired'); logout(); navigate('/'); }
                console.log('Failed to fetch data: ' + error);
                GlobalContext.stopSpinner();
            });
        } catch (er) {
            console.error('Error fetching items: ', er);
        }
    };

    const editUnitAsFavorite = async (item, flag) => {
        try {
            GlobalContext.startSpinner();
            let request = {
                Param1: String(item.Unit_Id)
            };
            await updateUnitAsFavorite(request).then((resp) => {
                fetchData();
                GlobalContext.stopSpinner();
                MySwal.fire(`${flag ? 'Pinned!' : 'Unpinned!'}`, `Your unit has been made as ${flag ? ' your pinned' : ' unpinned'}.`, 'success');
            }).catch((error) => {
                const status = error.response ? error.response.status : null;
                if (status === 403) { navigate('/forbidden'); }
                if (status === 401) { alert('session expired'); logout(); navigate('/'); }
                MySwal.fire('Error!', 'Something went wrong!', 'error');
                GlobalContext.stopSpinner();
            });
        } catch (er) {
            console.error('Error fetching items: ', er);
        }
    };


    const handleDelete = (item, flag) => {
        MySwal.fire({
            title: 'Are you sure?',
            text: `You want to make this unit as ${flag ? ' pinned' : ' unpinned'}.`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: `Yes, make it ${flag ? ' pinned!' : ' unpinned!'}`,
        }).then((result) => {
            if (result.isConfirmed) {
                editUnitAsFavorite(item,flag);
            }
        });
    };

    const handleChange = (event) => {
        let unit_id = event.target.value;
        let targetItem = listOfAllUnits.find(x => x.Unit_Id == Number(unit_id));
        targetItem.Selected=event.target.checked;
        if (event.target.checked) {
            setSelectedUnits([...selectedUnits, targetItem]);
        }
        else {
            setSelectedUnits((prevItems) =>
                prevItems.filter((item) => item !== targetItem)
            );
        }
    };

    const submit = () => {
        navigate('/patients', {
            state: { facilityObj: location.state.facilityObj,isMyFaciliitesTabActive:isMyFaciliitesTabActive,isPinndedTabActive:isPinndedTabActive, selectedUnits: selectedUnits,currentPageNo:1,searchTerm:'',isSorted:false }
        });
    }

    const back = () => {
        navigate('/facilities', {
            state: { isMyFaciliitesTabActive }
        });
    }

    return (
        <div className="col-lg-12 desktop-container p-0 d-flex flex-column">
            <a className="back-btn-link p-2 border-bottom fw-bold custom-color-maroon btn-link" onClick={back}>{facilityObj.Facility_Name}</a>
            <div className="text-center p-2 border-bottom fw-bold">Select Units</div>
            <ul className="nav nav-tabs bg-white" id="facilitiesTab" role="tablist">
                <li className="nav-item" role="presentation">
                    <button className={`nav-link ${isPinndedTabActive && 'active'}`} id="pinned-units-tab" data-bs-toggle="tab"
                        data-bs-target="#pinned-units-tab-pane" type="button" role="tab" aria-controls="pinned-units-tab-pane"
                        aria-selected="true" onClick={()=> setIsPinndedTabActive(true)}>Pinned</button>
                </li>
                <li className="nav-item" role="presentation">
                    <button className={`nav-link ${!isPinndedTabActive && 'active'}`} id="all-units-tab" data-bs-toggle="tab" data-bs-target="#all-units-tab-pane"
                        type="button" role="tab" aria-controls="all-units-tab-pane" aria-selected="false" onClick={()=> setIsPinndedTabActive(false)}>All Units</button>
                </li>
            </ul>
            <div className="row g-2">
                <div className="search-box col-12 px-3 pt-3 py-1">
                    <input type="text" className="form-control" placeholder="Search"
                      value={searchTerm} onChange={e => setSearchTerm(e.target.value)}/>
                </div>
            </div>
            <div className="tab-content pb-3 unit-list" id="facilitiesTabContent">
                <div className={`tab-pane fade ${isPinndedTabActive && 'show active'}`} id="pinned-units-tab-pane" role="tabpanel"
                    aria-labelledby="pinned-units-tab" tabIndex="0">
                    <div className="row g-sm-3 g-0">

                        {listOfAllUnits
                         .filter(item => item.Is_Favorite &&
                                Object.values(item).some(value => value !== null && value !== undefined &&
                                    value.toString().toLowerCase().includes(searchTerm.toLowerCase())
                                )
                          )
                            .map(item => (
                                <div className="col-lg-3 col-md-6 col-12" key={item.Unit_Id}>
                                    <div className="unit">
                                        <div className="form-check">
                                            <input className="form-check-input" type="checkbox" id={item.Unit_Id} value={item.Unit_Id}
                                                disabled={item.Patient_Count==0} checked={item.Selected} onChange={handleChange} />
                                            <label className="form-check-label" htmlFor={item.Unit_Id}></label>
                                        </div>
                                        <div className="unit-name">
                                            <span>{item.Unit_Name}</span>
                                            <p className="m-0 mt-1 small text-secondary">Patients: {item.Patient_Count}</p>
                                        </div>
                                        <div className="unit-icon"><FontAwesomeIcon onClick={() => handleDelete(item, false)} icon={faThumbtack} /></div>
                                    </div>
                                </div>
                            ))}
                    </div>
                </div>
                <div className={`tab-pane fade ${!isPinndedTabActive && 'show active'}`} id="all-units-tab-pane" role="tabpanel" aria-labelledby="all-units-tab"
                    tabIndex="0">
                    <div className="row g-sm-3 g-0">

                        {listOfAllUnits
                            .filter(item =>
                                Object.values(item).some(value => value !== null && value !== undefined &&
                                    value.toString().toLowerCase().includes(searchTerm.toLowerCase())
                                )
                            )
                            .map(item => (
                                <div className="col-lg-3 col-md-6 col-12" key={item.Unit_Id}>
                                    <div className="unit">
                                        <div className="form-check">
                                            <input className="form-check-input" type="checkbox" id={item.Unit_Id} value={item.Unit_Id}
                                                disabled={item.Patient_Count==0} checked={item.Selected} onChange={handleChange} />
                                            <label className="form-check-label" htmlFor={item.Unit_Id}></label>
                                        </div>
                                        <div className="unit-name">
                                            <span>{item.Unit_Name}</span>
                                            <p className="m-0 mt-1 small text-secondary">Patients: {item.Patient_Count}</p>
                                        </div>
                                        <div className="unit-icon">
                                            {item.Is_Favorite ?
                                                <FontAwesomeIcon onClick={() => handleDelete(item, false)} icon={faThumbtack} /> :
                                                <FontAwesomeIcon onClick={() => handleDelete(item, true)} icon={faThumbtackSlash} />
                                            }
                                        </div>
                                    </div>
                                </div>
                            ))}

                    </div>
                </div>
            </div>
            <div className="continue" id="continueBtn">
                <button className="btn btn-block" disabled={selectedUnits.length==0} onClick={submit}>Continue →</button>
            </div>
        </div>
    );
};
export default Units;